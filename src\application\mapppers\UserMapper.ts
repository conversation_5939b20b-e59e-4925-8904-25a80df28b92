// Mapper to convert between Prisma models and Domain entities
import { PTEIUser as PrismaUser } from '../../generated/prisma/index.js';
import { User } from '../../domain/entities/User.js';

export class UserMapper {
  // Convert from Prisma model to Domain entity
  static toDomain(prismaUser: PrismaUser): User {
    return new User(
      prismaUser.UserId,
      prismaUser.email,
      prismaUser.HashedPassword,
      prismaUser.FirstName,
      prismaUser.LastName,
      prismaUser.UserType,
      prismaUser.AccountStatus,
      prismaUser.IsAccountLockedOut,
      prismaUser.FailedLoginAttempts,
      prismaUser.IsUserLoggedIn,
      prismaUser.UserCreatedAt,
      prismaUser.UserModifiedAt || undefined,
      prismaUser.RoleId || undefined,
      prismaUser.DepartmentId || undefined,
      prismaUser.UserProfilePicPath,
      prismaUser.EmailVerificationToken || '',
      prismaUser.EmailVerificationTokenExpiry || undefined,
      prismaUser.PasswordResetToken || '',
      prismaUser.PasswordResetTokenExpiry || undefined
    );
  }

  // Convert from Domain entity to Prisma model data
  static toPrisma(domainUser: User): Omit<PrismaUser, 'Role' | 'Department' | 'AuditLogs' | 'UniversityUsers'> {
    return {
      UserId: domainUser.id,
      email: domainUser.email,
      HashedPassword: domainUser.hashedPassword,
      FirstName: domainUser.firstName,
      LastName: domainUser.lastName,
      UserType: domainUser.userType,
      AccountStatus: domainUser.accountStatus,
      IsAccountLockedOut: domainUser.isAccountLockedOut,
      FailedLoginAttempts: domainUser.failedLoginAttempts,
      IsUserLoggedIn: domainUser.isUserLoggedIn,
      UserCreatedAt: domainUser.userCreatedAt,
      UserModifiedAt: domainUser.userModifiedAt || null,
      RoleId: domainUser.roleId || null,
      DepartmentId: domainUser.departmentId || null,
      UserProfilePicPath: domainUser.userProfilePicPath,
      EmailVerificationToken: domainUser.emailVerificationToken || null,
      EmailVerificationTokenExpiry: domainUser.emailVerificationTokenExpiry || null,
      PasswordResetToken: domainUser.passwordResetToken || null,
      PasswordResetTokenExpiry: domainUser.passwordResetTokenExpiry || null
    };
  }

  // Convert array of Prisma models to Domain entities
  static toDomainArray(prismaUsers: PrismaUser[]): User[] {
    return prismaUsers.map(user => this.toDomain(user));
  }
}
