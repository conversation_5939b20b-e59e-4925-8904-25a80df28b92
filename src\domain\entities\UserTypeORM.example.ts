// Example of TypeORM Entity approach (requires TypeORM installation)
// This is just an example - you'd need to install TypeORM first

/*
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';

@Entity('PTEIUser')
export class User {
  @PrimaryGeneratedColumn('uuid')
  UserId!: string;

  @Column({ type: 'nvarchar', length: 1000, unique: true })
  email!: string;

  @Column({ type: 'nvarchar', length: 1000 })
  HashedPassword!: string;

  @Column({ type: 'nvarchar', length: 1000, default: '' })
  FirstName!: string;

  @Column({ type: 'nvarchar', length: 1000, default: '' })
  LastName!: string;

  @Column({ type: 'nvarchar', length: 1000, default: '' })
  UserProfilePicPath!: string;

  @Column({ type: 'nvarchar', length: 1000 })
  UserType!: string;

  @Column({ type: 'nvarchar', length: 1000, default: '' })
  AccountStatus!: string;

  @Column({ type: 'bit', default: false })
  IsAccountLockedOut!: boolean;

  @Column({ type: 'int', default: 0 })
  FailedLoginAttempts!: number;

  @Column({ type: 'bit', default: false })
  IsUserLoggedIn!: boolean;

  @CreateDateColumn({ type: 'datetimeoffset' })
  UserCreatedAt!: Date;

  @UpdateDateColumn({ type: 'datetimeoffset', nullable: true })
  UserModifiedAt?: Date;

  @Column({ type: 'nvarchar', length: 1000, default: '', nullable: true })
  EmailVerificationToken?: string;

  @Column({ type: 'datetimeoffset', nullable: true })
  EmailVerificationTokenExpiry?: Date;

  @Column({ type: 'nvarchar', length: 1000, default: '', nullable: true })
  PasswordResetToken?: string;

  @Column({ type: 'datetimeoffset', nullable: true })
  PasswordResetTokenExpiry?: Date;

  @Column({ type: 'uniqueidentifier', nullable: true })
  RoleId?: string;

  @Column({ type: 'uniqueidentifier', nullable: true })
  DepartmentId?: string;

  // Relations
  @ManyToOne(() => Role, { nullable: true })
  @JoinColumn({ name: 'RoleId' })
  Role?: Role;

  @ManyToOne(() => Department, { nullable: true })
  @JoinColumn({ name: 'DepartmentId' })
  Department?: Department;
}

@Entity('Role')
export class Role {
  @PrimaryGeneratedColumn('uuid')
  RoleId!: string;

  @Column({ type: 'nvarchar', length: 1000 })
  RoleName!: string;

  @Column({ type: 'nvarchar', length: 1000, nullable: true })
  Description?: string;

  @Column({ type: 'bit', default: true })
  IsActive!: boolean;

  @CreateDateColumn({ type: 'datetimeoffset' })
  CreatedAt!: Date;
}

@Entity('Department')
export class Department {
  @PrimaryGeneratedColumn('uuid')
  DepartmentId!: string;

  @Column({ type: 'nvarchar', length: 1000 })
  DepartmentName!: string;

  @Column({ type: 'nvarchar', length: 1000, nullable: true })
  Description?: string;

  @Column({ type: 'bit', default: true })
  IsActive!: boolean;

  @CreateDateColumn({ type: 'datetimeoffset' })
  CreatedAt!: Date;
}
*/

// Note: To use TypeORM, you would need to:
// 1. npm install typeorm reflect-metadata mssql
// 2. Update your tsconfig.json to enable decorators
// 3. Replace Prisma setup with TypeORM configuration
// 4. Create a data source configuration file
