/*
  Warnings:

  - You are about to drop the `AuditLog` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Department` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Permission` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `PTEIUser` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Role` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `RolePermission` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `University` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `UniversityUser` table. If the table is not empty, all the data it contains will be lost.

*/
BEGIN TRY

BEGIN TRAN;

-- DropForeignKey
ALTER TABLE [dbo].[AuditLog] DROP CONSTRAINT [AuditLog_UserId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[PTEIUser] DROP CONSTRAINT [PTEIUser_DepartmentId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[PTEIUser] DROP CONSTRAINT [PTEIUser_RoleId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[RolePermission] DROP CONSTRAINT [RolePermission_PermissionId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[RolePermission] DROP CONSTRAINT [RolePermission_RoleId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[UniversityUser] DROP CONSTRAINT [UniversityUser_UniversityId_fkey];

-- DropForeignKey
ALTER TABLE [dbo].[UniversityUser] DROP CONSTRAINT [UniversityUser_UserId_fkey];

-- DropTable
DROP TABLE [dbo].[AuditLog];

-- DropTable
DROP TABLE [dbo].[Department];

-- DropTable
DROP TABLE [dbo].[Permission];

-- DropTable
DROP TABLE [dbo].[PTEIUser];

-- DropTable
DROP TABLE [dbo].[Role];

-- DropTable
DROP TABLE [dbo].[RolePermission];

-- DropTable
DROP TABLE [dbo].[University];

-- DropTable
DROP TABLE [dbo].[UniversityUser];

-- CreateTable
CREATE TABLE [dbo].[User] (
    [id] INT NOT NULL IDENTITY(1,1),
    [name] NVARCHAR(1000) NOT NULL,
    [email] NVARCHAR(1000) NOT NULL,
    [password] NVARCHAR(1000) NOT NULL,
    [createdAt] DATETIME2 NOT NULL CONSTRAINT [User_createdAt_df] DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT [User_pkey] PRIMARY KEY CLUSTERED ([id]),
    CONSTRAINT [User_email_key] UNIQUE NONCLUSTERED ([email])
);

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH
