/*
  Warnings:

  - You are about to drop the `User` table. If the table is not empty, all the data it contains will be lost.

*/
BEGIN TRY

BEGIN TRAN;

-- DropTable
DROP TABLE [dbo].[User];

-- CreateTable
CREATE TABLE [dbo].[PTEIUser] (
    [UserId] UNIQUEIDENTIFIER NOT NULL,
    [email] NVARCHAR(1000) NOT NULL,
    [HashedPassword] NVARCHAR(1000) NOT NULL,
    [FirstName] NVARCHAR(1000) NOT NULL CONSTRAINT [PTEIUser_FirstName_df] DEFAULT '',
    [LastName] NVARCHAR(1000) NOT NULL CONSTRAINT [PTEIUser_LastName_df] DEFAULT '',
    [UserProfilePicPath] NVARCHAR(1000) NOT NULL CONSTRAINT [PTEIUser_UserProfilePicPath_df] DEFAULT '',
    [UserType] NVARCHAR(1000) NOT NULL,
    [AccountStatus] NVARCHAR(1000) NOT NULL CONSTRAINT [PTEIUser_AccountStatus_df] DEFAULT '',
    [IsAccountLockedOut] BIT NOT NULL CONSTRAINT [PTEIUser_IsAccountLockedOut_df] DEFAULT 0,
    [FailedLoginAttempts] INT NOT NULL CONSTRAINT [PTEIUser_FailedLoginAttempts_df] DEFAULT 0,
    [IsUserLoggedIn] BIT NOT NULL CONSTRAINT [PTEIUser_IsUserLoggedIn_df] DEFAULT 0,
    [UserCreatedAt] DATETIMEOFFSET NOT NULL CONSTRAINT [PTEIUser_UserCreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [UserModifiedAt] DATETIMEOFFSET,
    [EmailVerificationToken] NVARCHAR(1000) CONSTRAINT [PTEIUser_EmailVerificationToken_df] DEFAULT '',
    [EmailVerificationTokenExpiry] DATETIMEOFFSET,
    [PasswordResetToken] NVARCHAR(1000) CONSTRAINT [PTEIUser_PasswordResetToken_df] DEFAULT '',
    [PasswordResetTokenExpiry] DATETIMEOFFSET,
    [RoleId] UNIQUEIDENTIFIER,
    [DepartmentId] UNIQUEIDENTIFIER,
    CONSTRAINT [PTEIUser_pkey] PRIMARY KEY CLUSTERED ([UserId]),
    CONSTRAINT [PTEIUser_email_key] UNIQUE NONCLUSTERED ([email])
);

-- CreateTable
CREATE TABLE [dbo].[Role] (
    [RoleId] UNIQUEIDENTIFIER NOT NULL,
    [RoleName] NVARCHAR(1000) NOT NULL,
    [Description] NVARCHAR(1000),
    [IsActive] BIT NOT NULL CONSTRAINT [Role_IsActive_df] DEFAULT 1,
    [CreatedAt] DATETIMEOFFSET NOT NULL CONSTRAINT [Role_CreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT [Role_pkey] PRIMARY KEY CLUSTERED ([RoleId])
);

-- CreateTable
CREATE TABLE [dbo].[UniversityUser] (
    [UniversityUserId] UNIQUEIDENTIFIER NOT NULL,
    [CreatedAt] DATETIMEOFFSET NOT NULL CONSTRAINT [UniversityUser_CreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [IsActive] BIT NOT NULL CONSTRAINT [UniversityUser_IsActive_df] DEFAULT 1,
    [UserId] UNIQUEIDENTIFIER NOT NULL,
    [UniversityId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [UniversityUser_pkey] PRIMARY KEY CLUSTERED ([UniversityUserId])
);

-- CreateTable
CREATE TABLE [dbo].[University] (
    [UniversityId] UNIQUEIDENTIFIER NOT NULL,
    [UniversityName] NVARCHAR(1000) NOT NULL,
    [PrimaryEmail] NVARCHAR(1000),
    [CCEmails] NVARCHAR(1000),
    [BCCEmails] NVARCHAR(1000),
    [ContactPerson] NVARCHAR(1000),
    [Phone] NVARCHAR(1000),
    [IsActive] BIT NOT NULL CONSTRAINT [University_IsActive_df] DEFAULT 1,
    [CreatedAt] DATETIMEOFFSET NOT NULL CONSTRAINT [University_CreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    [UpdatedAt] DATETIMEOFFSET NOT NULL,
    CONSTRAINT [University_pkey] PRIMARY KEY CLUSTERED ([UniversityId])
);

-- CreateTable
CREATE TABLE [dbo].[RolePermission] (
    [RoleId] UNIQUEIDENTIFIER NOT NULL,
    [PermissionId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [RolePermission_pkey] PRIMARY KEY CLUSTERED ([RoleId],[PermissionId])
);

-- CreateTable
CREATE TABLE [dbo].[Permission] (
    [PermissionId] UNIQUEIDENTIFIER NOT NULL,
    [PermissionName] NVARCHAR(1000) NOT NULL,
    [Description] NVARCHAR(1000),
    CONSTRAINT [Permission_pkey] PRIMARY KEY CLUSTERED ([PermissionId])
);

-- CreateTable
CREATE TABLE [dbo].[Department] (
    [DepartmentId] UNIQUEIDENTIFIER NOT NULL,
    [DepartmentName] NVARCHAR(1000) NOT NULL,
    [Description] NVARCHAR(1000),
    [IsActive] BIT NOT NULL CONSTRAINT [Department_IsActive_df] DEFAULT 1,
    [CreatedAt] DATETIMEOFFSET NOT NULL CONSTRAINT [Department_CreatedAt_df] DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT [Department_pkey] PRIMARY KEY CLUSTERED ([DepartmentId])
);

-- CreateTable
CREATE TABLE [dbo].[AuditLog] (
    [LogId] UNIQUEIDENTIFIER NOT NULL,
    [ActionType] NVARCHAR(1000) NOT NULL,
    [TableName] NVARCHAR(1000) NOT NULL,
    [RecordId] NVARCHAR(1000) NOT NULL,
    [OldValues] NVARCHAR(1000),
    [NewValues] NVARCHAR(1000),
    [IPAddress] NVARCHAR(1000) NOT NULL,
    [SessionId] NVARCHAR(1000) NOT NULL,
    [UserAgent] NVARCHAR(1000) NOT NULL,
    [ActionTimestamp] DATETIMEOFFSET NOT NULL CONSTRAINT [AuditLog_ActionTimestamp_df] DEFAULT CURRENT_TIMESTAMP,
    [AdditionalInfo] NVARCHAR(1000),
    [UserId] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [AuditLog_pkey] PRIMARY KEY CLUSTERED ([LogId])
);

-- AddForeignKey
ALTER TABLE [dbo].[PTEIUser] ADD CONSTRAINT [PTEIUser_RoleId_fkey] FOREIGN KEY ([RoleId]) REFERENCES [dbo].[Role]([RoleId]) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[PTEIUser] ADD CONSTRAINT [PTEIUser_DepartmentId_fkey] FOREIGN KEY ([DepartmentId]) REFERENCES [dbo].[Department]([DepartmentId]) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[UniversityUser] ADD CONSTRAINT [UniversityUser_UserId_fkey] FOREIGN KEY ([UserId]) REFERENCES [dbo].[PTEIUser]([UserId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[UniversityUser] ADD CONSTRAINT [UniversityUser_UniversityId_fkey] FOREIGN KEY ([UniversityId]) REFERENCES [dbo].[University]([UniversityId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[RolePermission] ADD CONSTRAINT [RolePermission_RoleId_fkey] FOREIGN KEY ([RoleId]) REFERENCES [dbo].[Role]([RoleId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[RolePermission] ADD CONSTRAINT [RolePermission_PermissionId_fkey] FOREIGN KEY ([PermissionId]) REFERENCES [dbo].[Permission]([PermissionId]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[AuditLog] ADD CONSTRAINT [AuditLog_UserId_fkey] FOREIGN KEY ([UserId]) REFERENCES [dbo].[PTEIUser]([UserId]) ON DELETE NO ACTION ON UPDATE CASCADE;

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH
