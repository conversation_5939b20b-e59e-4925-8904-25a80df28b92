// Domain Entity for User
export class User {
  constructor(
    public readonly id: string,
    public readonly email: string,
    private _hashedPassword: string,
    public readonly firstName: string,
    public readonly lastName: string,
    public readonly userType: string,
    public readonly accountStatus: string = 'ACTIVE',
    public readonly isAccountLockedOut: boolean = false,
    public readonly failedLoginAttempts: number = 0,
    public readonly isUserLoggedIn: boolean = false,
    public readonly userCreatedAt: Date = new Date(),
    public readonly userModifiedAt?: Date,
    public readonly roleId?: string,
    public readonly departmentId?: string,
    public readonly userProfilePicPath: string = '',
    public readonly emailVerificationToken: string = '',
    public readonly emailVerificationTokenExpiry?: Date,
    public readonly passwordResetToken: string = '',
    public readonly passwordResetTokenExpiry?: Date
  ) {}

  // Domain methods
  public updatePassword(newHashedPassword: string): User {
    return new User(
      this.id,
      this.email,
      newHashedPassword,
      this.firstName,
      this.lastName,
      this.userType,
      this.accountStatus,
      this.isAccountLockedOut,
      0, // Reset failed attempts
      this.isUserLoggedIn,
      this.userCreatedAt,
      new Date(), // Update modified date
      this.roleId,
      this.departmentId,
      this.userProfilePicPath,
      this.emailVerificationToken,
      this.emailVerificationTokenExpiry,
      this.passwordResetToken,
      this.passwordResetTokenExpiry
    );
  }

  public lockAccount(): User {
    return new User(
      this.id,
      this.email,
      this._hashedPassword,
      this.firstName,
      this.lastName,
      this.userType,
      'LOCKED',
      true,
      this.failedLoginAttempts,
      false,
      this.userCreatedAt,
      new Date(),
      this.roleId,
      this.departmentId,
      this.userProfilePicPath,
      this.emailVerificationToken,
      this.emailVerificationTokenExpiry,
      this.passwordResetToken,
      this.passwordResetTokenExpiry
    );
  }

  public incrementFailedLoginAttempts(): User {
    const newFailedAttempts = this.failedLoginAttempts + 1;
    const shouldLock = newFailedAttempts >= 5;

    return new User(
      this.id,
      this.email,
      this._hashedPassword,
      this.firstName,
      this.lastName,
      this.userType,
      shouldLock ? 'LOCKED' : this.accountStatus,
      shouldLock,
      newFailedAttempts,
      this.isUserLoggedIn,
      this.userCreatedAt,
      new Date(),
      this.roleId,
      this.departmentId,
      this.userProfilePicPath,
      this.emailVerificationToken,
      this.emailVerificationTokenExpiry,
      this.passwordResetToken,
      this.passwordResetTokenExpiry
    );
  }

  // Getters
  public get hashedPassword(): string {
    return this._hashedPassword;
  }

  public get fullName(): string {
    return `${this.firstName} ${this.lastName}`.trim();
  }

  public get isLocked(): boolean {
    return this.isAccountLockedOut || this.accountStatus === 'LOCKED';
  }
}
